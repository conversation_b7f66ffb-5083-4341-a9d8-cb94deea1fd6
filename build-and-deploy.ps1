# PowerShell script for Windows
# Configuration - Update these values
$ECR_REPOSITORY_URI = "953265903196.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda"
$AWS_REGION = "us-east-1"
$LAMBDA_FUNCTION_NAME = "IBE-PreProd-PDF-Splitting-Processor"

Write-Host "Building and deploying Lambda container image..." -ForegroundColor Green

# Navigate to lambda_container directory
Set-Location lambda_container

# Login to ECR
Write-Host "Logging in to ECR..." -ForegroundColor Yellow
$loginCommand = aws ecr get-login-password --region $AWS_REGION
$loginCommand | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI

# Build the image with explicit platform for Lambda compatibility
Write-Host "Building Docker image..." -ForegroundColor Yellow
docker build --platform linux/amd64 -t $LAMBDA_FUNCTION_NAME .

# Tag the image
Write-Host "Tagging image..." -ForegroundColor Yellow
docker tag "${LAMBDA_FUNCTION_NAME}:latest" "${ECR_REPOSITORY_URI}:latest"

# Push the image
Write-Host "Pushing image to ECR..." -ForegroundColor Yellow
docker push "${ECR_REPOSITORY_URI}:latest"

Write-Host "Image pushed successfully!" -ForegroundColor Green
Write-Host "ECR Image URI: ${ECR_REPOSITORY_URI}:latest" -ForegroundColor Cyan

# Create or update Lambda function
Write-Host "Creating/updating Lambda function..." -ForegroundColor Yellow

$createResult = aws lambda create-function `
    --function-name $LAMBDA_FUNCTION_NAME `
    --package-type Image `
    --code ImageUri="${ECR_REPOSITORY_URI}:latest" `
    --role "arn:aws:iam::953265903196:role/IBE-PreProd-PDF-Splitting-Processor" `
    --timeout 900 `
    --memory-size 3008 `
    --region $AWS_REGION `
    --environment 'Variables={
        "DEV_BASE_URL":"https://dev-api.example.com",
        "SQA_BASE_URL":"https://sqa-api.example.com", 
        "PROD_BASE_URL":"https://prod-api.example.com",
        "SQS_QUEUE_URL":"https://sqs.us-east-1.amazonaws.com/953265903196/error-queue"
    }' 2>$null

# If create failed (function exists), update it instead
if ($LASTEXITCODE -ne 0) {
    Write-Host "Function exists, updating..." -ForegroundColor Yellow
    
    aws lambda update-function-code `
        --function-name $LAMBDA_FUNCTION_NAME `
        --image-uri "${ECR_REPOSITORY_URI}:latest" `
        --region $AWS_REGION
        
    aws lambda update-function-configuration `
        --function-name $LAMBDA_FUNCTION_NAME `
        --timeout 900 `
        --memory-size 3008 `
        --region $AWS_REGION `
        --environment 'Variables={
            "DEV_BASE_URL":"https://dev-api.example.com",
            "SQA_BASE_URL":"https://sqa-api.example.com", 
            "PROD_BASE_URL":"https://prod-api.example.com",
            "SQS_QUEUE_URL":"https://sqs.us-east-1.amazonaws.com/953265903196/error-queue"
        }'
}

Write-Host "Lambda function deployment complete!" -ForegroundColor Green
Write-Host "Function Name: $LAMBDA_FUNCTION_NAME" -ForegroundColor Cyan
Write-Host "Image URI: ${ECR_REPOSITORY_URI}:latest" -ForegroundColor Cyan

# Navigate back
Set-Location ..
