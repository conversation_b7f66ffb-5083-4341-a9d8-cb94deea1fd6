# PowerShell script to build and push Docker image only
# Configuration - Update these values
$ECR_REPOSITORY_URI = "953265903196.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda"
$AWS_REGION = "us-east-1"
$IMAGE_TAG = "latest"

Write-Host "Building and pushing Docker image to ECR..." -ForegroundColor Green

# Navigate to lambda_container directory
Set-Location lambda_container

# Login to ECR
Write-Host "Logging in to ECR..." -ForegroundColor Yellow
$loginCommand = aws ecr get-login-password --region $AWS_REGION
$loginCommand | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI

if ($LASTEXITCODE -ne 0) {
    Write-Host "ECR login failed!" -ForegroundColor Red
    exit 1
}

# Build the image with explicit platform for Lambda compatibility
Write-Host "Building Docker image with linux/amd64 platform..." -ForegroundColor Yellow
docker build --platform linux/amd64 -t "lambda-image:$IMAGE_TAG" .

if ($LASTEXITCODE -ne 0) {
    Write-Host "Docker build failed!" -ForegroundColor Red
    exit 1
}

# Tag the image for ECR
Write-Host "Tagging image for ECR..." -ForegroundColor Yellow
docker tag "lambda-image:$IMAGE_TAG" "${ECR_REPOSITORY_URI}:$IMAGE_TAG"

# Push the image
Write-Host "Pushing image to ECR..." -ForegroundColor Yellow
docker push "${ECR_REPOSITORY_URI}:$IMAGE_TAG"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Image pushed successfully!" -ForegroundColor Green
    Write-Host "ECR Image URI: ${ECR_REPOSITORY_URI}:$IMAGE_TAG" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "You can now create a Lambda function using this image URI:" -ForegroundColor White
    Write-Host "${ECR_REPOSITORY_URI}:$IMAGE_TAG" -ForegroundColor Yellow
} else {
    Write-Host "❌ Image push failed!" -ForegroundColor Red
    exit 1
}

# Navigate back
Set-Location ..
