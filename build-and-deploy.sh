#!/bin/bash

# Configuration - Update these values
ECR_REPOSITORY_URI="953265903196.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda"
AWS_REGION="us-east-1"
LAMBDA_FUNCTION_NAME="ocr-preprod-split-lambda"

echo "Building and deploying Lambda container image..."

# Navigate to lambda_container directory
cd lambda_container

# Login to ECR
echo "Logging in to ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI

# Build the image with explicit platform for Lambda compatibility
echo "Building Docker image..."
docker build --platform linux/amd64 -t $LAMBDA_FUNCTION_NAME .

# Tag the image
echo "Tagging image..."
docker tag $LAMBDA_FUNCTION_NAME:latest $ECR_REPOSITORY_URI:latest

# Push the image
echo "Pushing image to ECR..."
docker push $ECR_REPOSITORY_URI:latest

echo "Image pushed successfully!"
echo "ECR Image URI: $ECR_REPOSITORY_URI:latest"

# Create or update Lambda function
echo "Creating/updating Lambda function..."
aws lambda create-function \
    --function-name $LAMBDA_FUNCTION_NAME \
    --package-type Image \
    --code ImageUri=$ECR_REPOSITORY_URI:latest \
    --role arn:aws:iam::953265903196:role/lambda-execution-role \
    --timeout 900 \
    --memory-size 3008 \
    --region $AWS_REGION \
    --environment Variables='{
        "DEV_BASE_URL":"https://dev-api.example.com",
        "SQA_BASE_URL":"https://sqa-api.example.com", 
        "PROD_BASE_URL":"https://prod-api.example.com",
        "SQS_QUEUE_URL":"https://sqs.us-east-1.amazonaws.com/953265903196/error-queue"
    }' 2>/dev/null

# If create failed (function exists), update it instead
if [ $? -ne 0 ]; then
    echo "Function exists, updating..."
    aws lambda update-function-code \
        --function-name $LAMBDA_FUNCTION_NAME \
        --image-uri $ECR_REPOSITORY_URI:latest \
        --region $AWS_REGION
        
    aws lambda update-function-configuration \
        --function-name $LAMBDA_FUNCTION_NAME \
        --timeout 900 \
        --memory-size 3008 \
        --region $AWS_REGION \
        --environment Variables='{
            "DEV_BASE_URL":"https://dev-api.example.com",
            "SQA_BASE_URL":"https://sqa-api.example.com", 
            "PROD_BASE_URL":"https://prod-api.example.com",
            "SQS_QUEUE_URL":"https://sqs.us-east-1.amazonaws.com/953265903196/error-queue"
        }'
fi

echo "Lambda function deployment complete!"
echo "Function Name: $LAMBDA_FUNCTION_NAME"
echo "Image URI: $ECR_REPOSITORY_URI:latest"
