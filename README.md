# Lambda Container Deployment

This repository contains a Lambda function for PDF splitting using PyMuPDF, packaged as a Docker container.

## Issue Resolution: Image Manifest Media Type Error

The error you encountered:
```
The image manifest, config or layer media type for the source image ... is not supported
```

This typically occurs due to:
1. Multi-platform builds creating incompatible manifests
2. Docker buildx using unsupported manifest formats
3. Missing platform specification for Lambda

## Solution

### Prerequisites
- AWS CLI configured with appropriate permissions
- Docker installed and running
- ECR repository created (which you've already done)

### Quick Fix - Build and Push Image

**For Windows (PowerShell):**
```powershell
.\build-image.ps1
```

**For Linux/Mac (Bash):**
```bash
chmod +x build-and-deploy.sh
./build-and-deploy.sh
```

### Manual Steps

If you prefer to run commands manually:

1. **Navigate to lambda_container directory:**
   ```bash
   cd lambda_container
   ```

2. **Login to ECR:**
   ```bash
   aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 953265903196.dkr.ecr.us-east-1.amazonaws.com
   ```

3. **Build with explicit platform (IMPORTANT):**
   ```bash
   docker build --platform linux/amd64 -t lambda-image .
   ```

4. **Tag for ECR:**
   ```bash
   docker tag lambda-image:latest 953265903196.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda:latest
   ```

5. **Push to ECR:**
   ```bash
   docker push 953265903196.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda:latest
   ```

### Create Lambda Function

After successful push, create the Lambda function:

```bash
aws lambda create-function \
    --function-name ocr-preprod-split-lambda \
    --package-type Image \
    --code ImageUri=953265903196.dkr.ecr.us-east-1.amazonaws.com/ocr-preprod-split-lambda:latest \
    --role arn:aws:iam::953265903196:role/lambda-execution-role \
    --timeout 900 \
    --memory-size 3008 \
    --region us-east-1
```

### Key Points for Success

1. **Always use `--platform linux/amd64`** when building for Lambda
2. **Avoid Docker buildx** for Lambda images unless specifically configured
3. **Use standard docker build** command, not buildx
4. **Ensure ECR login is successful** before pushing

### Environment Variables

Update the environment variables in the deployment scripts:
- `DEV_BASE_URL`
- `SQA_BASE_URL` 
- `PROD_BASE_URL`
- `SQS_QUEUE_URL`

### IAM Role Requirements

Your Lambda execution role needs permissions for:
- S3 read/write access
- SQS send message
- CloudWatch Logs
- ECR image pull

### Testing

Test the function with a sample event:
```json
{
  "bucket": "source-bucket-name",
  "key": "path/to/file.pdf",
  "company_id": "12345",
  "environment": "PROD",
  "original_attachment_id": "67890",
  "uses_ocr": true,
  "original_file_name": "document.pdf"
}
```

## Files

- `lambda_container/Dockerfile` - Container definition
- `lambda_container/lambda_function.py` - Main Lambda function
- `lambda_container/requirements.txt` - Python dependencies
- `build-image.ps1` - PowerShell build script
- `build-and-deploy.sh` - Bash build and deploy script
- `build-and-deploy.ps1` - PowerShell build and deploy script
